import requests
import pandas as pd
import pdfplumber
import re
import os
import time
import csv
import random
from tqdm import tqdm
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from fake_useragent import UserAgent

# 配置代理（如果需要）
PROXIES = None  # 可以设置为 {'http': 'http://your_proxy:port', 'https': 'https://your_proxy:port'}

# 配置请求参数
MAX_RETRIES = 3
BACKOFF_FACTOR = 2
REQUEST_TIMEOUT = 30
THREAD_WORKERS = 3  # 降低并发数以减少被封风险
DELAY_BETWEEN_REQUESTS = 5  # 基础请求间隔（秒）
RANDOM_DELAY_RANGE = (1, 5)  # 随机延迟范围

# 创建会话对象
def create_session():
    session = requests.Session()
    
    # 配置重试策略
    retry_strategy = Retry(
        total=MAX_RETRIES,
        backoff_factor=BACKOFF_FACTOR,
        status_forcelist=[408, 429, 500, 502, 503, 504],
        allowed_methods=["GET", "POST"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session

# 随机UserAgent
ua = UserAgent()

# 创建保存文件的文件夹
os.makedirs('reports', exist_ok=True)
os.makedirs('csv_data', exist_ok=True)

def test_api_connectivity():
    """测试API连接性"""
    test_url = "http://www.cninfo.com.cn"
    try:
        session = create_session()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        response = session.get(test_url, headers=headers, timeout=10)
        print(f"API连接测试 - 状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        return response.status_code == 200
    except Exception as e:
        print(f"API连接测试失败: {e}")
        return False

def get_companies_alternative():
    """使用替代方法获取公司列表"""
    # 尝试多个数据源
    alternative_sources = [
        "https://api.cninfo.com.cn/api/stock/p_public0002",
        "http://push2.eastmoney.com/api/qt/clist/get?pn=1&pz=5000&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f12,f14"
    ]

    for source_url in alternative_sources:
        try:
            session = create_session()
            headers = {
                'User-Agent': ua.random,
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }

            response = session.get(source_url, headers=headers, timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'eastmoney' in source_url and 'data' in data and 'diff' in data['data']:
                        # 东方财富数据格式
                        companies = []
                        for item in data['data']['diff']:
                            companies.append({
                                'code': item['f12'],
                                'name': item['f14'],
                                'market': 'szse' if item['f12'].startswith(('000', '002', '300')) else 'sse'
                            })
                        return companies
                    elif 'cninfo' in source_url:
                        # 巨潮数据格式
                        if 'records' in data:
                            companies = []
                            for item in data['records']:
                                companies.append({
                                    'code': item.get('SECCODE', ''),
                                    'name': item.get('SECNAME', ''),
                                    'market': 'szse' if item.get('SECCODE', '').startswith(('000', '002', '300')) else 'sse'
                                })
                            return companies
                except:
                    continue
        except:
            continue

    return []

def get_all_companies():
    """获取所有上市公司列表（带备用方案）"""
    print("尝试获取上市公司列表...")

    # 首先尝试替代数据源
    companies = get_companies_alternative()
    if companies:
        print(f"从替代数据源获取到{len(companies)}家公司")
        return companies

    print("替代数据源失败，使用备用公司列表")
    return load_backup_companies()

def search_annual_reports(stock_code, years=range(2014, 2024)):
    """搜索指定年份的年报（带智能延迟）"""
    base_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    reports = []
    
    for year in years:
        params = {
            'stock': stock_code,
            'category': 'category_ndbg_szsh',
            'pageSize': 30,
            'pageNum': 1,
            'column': 'szse',
            'seDate': f'{year}-01-01~{year}-12-31'
        }
        
        try:
            session = create_session()
            headers = {
                'User-Agent': ua.random,
                'Referer': 'http://www.cninfo.com.cn/new/commonUrl',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'X-Requested-With': 'XMLHttpRequest',
                'Connection': 'keep-alive'
            }
            
            # 随机延迟
            time.sleep(DELAY_BETWEEN_REQUESTS + random.uniform(*RANDOM_DELAY_RANGE))
            
            response = session.get(base_url, params=params, headers=headers,
                                 timeout=REQUEST_TIMEOUT, proxies=PROXIES)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '').lower()
            if 'application/json' not in content_type and 'text/javascript' not in content_type:
                print(f"警告：搜索{stock_code} {year}年年报时响应不是JSON格式")
                debug_response(response, f"搜索年报-{stock_code}-{year}")
                continue

            try:
                data = response.json()
            except ValueError:
                print(f"搜索年报时无法解析JSON响应 - 股票代码: {stock_code}, 年份: {year}")
                debug_response(response, f"搜索年报JSON解析失败-{stock_code}-{year}")
                continue
            
            if data.get('success') and data.get('totalAnnouncement', 0) > 0:
                for ann in data['announcements']:
                    if '年报' in ann['announcementTitle'] and '摘要' not in ann['announcementTitle']:
                        pdf_url = f"http://www.cninfo.com.cn/new/disclosure/detail?plate=szse&stockCode={ann['secCode']}&announcementId={ann['announcementId']}"
                        reports.append({
                            'title': ann['announcementTitle'],
                            'url': pdf_url,
                            'code': ann['secCode'],
                            'name': ann['secName'],
                            'date': ann['announcementTime'],
                            'year': year
                        })
        except requests.exceptions.RequestException as e:
            print(f"搜索{stock_code} {year}年年报时出错: {e}")
            if isinstance(e, requests.exceptions.HTTPError) and e.response.status_code == 500:
                # 遇到500错误时增加延迟
                time.sleep(30)
            continue
        except Exception as e:
            print(f"搜索{stock_code} {year}年年报时未知错误: {e}")
            continue
    
    return reports

def download_pdf(url, filename):
    """下载PDF文件（带智能重试）"""
    attempts = 0
    while attempts < MAX_RETRIES:
        try:
            session = create_session()
            headers = {
                'User-Agent': ua.random,
                'Referer': 'http://www.cninfo.com.cn/new/commonUrl'
            }
            
            # 随机延迟
            time.sleep(DELAY_BETWEEN_REQUESTS + random.uniform(*RANDOM_DELAY_RANGE))
            
            response = session.get(url, headers=headers, stream=True, 
                                 timeout=REQUEST_TIMEOUT, proxies=PROXIES)
            response.raise_for_status()
            
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            return True
        except requests.exceptions.RequestException as e:
            print(f"下载PDF时出错(尝试{attempts+1}/{MAX_RETRIES}): {e}")
            attempts += 1
            if isinstance(e, requests.exceptions.HTTPError) and e.response.status_code == 500:
                time.sleep(30)
            else:
                time.sleep(10)
        except Exception as e:
            print(f"下载PDF时未知错误: {e}")
            attempts += 1
            time.sleep(10)
    
    return False

def process_company(company):
    """处理单个公司的年报（带智能错误处理）"""
    try:
        reports = search_annual_reports(company['code'])
        if not reports:
            print(f"未找到{company['code']}的年报")
            return None
        
        # 只处理最新的年报
        latest_report = reports[0]
        pdf_filename = f"reports/{latest_report['code']}_{latest_report['year']}.pdf"
        
        if not os.path.exists(pdf_filename):
            if not download_pdf(latest_report['url'], pdf_filename):
                return None
        
        text = extract_text_from_pdf(pdf_filename)
        if not text.strip():
            print(f"警告: {latest_report['code']} 的PDF文本提取为空")
            return None
        
        extracted_data = extract_fields(text, latest_report)
        return extracted_data
        
    except Exception as e:
        print(f"处理{company['code']}时严重错误: {e}")
        return None

def main():
    # 获取所有上市公司
    print("获取上市公司列表...")
    all_companies = get_all_companies()
    if not all_companies:
        print("无法获取上市公司列表，程序终止")
        return

    print(f"共获取到{len(all_companies)}家上市公司")

    # 限制处理数量（先用小样本测试）
    sample_size = min(50, len(all_companies))  # 先测试50家公司
    companies_to_process = all_companies[:sample_size]

    print(f"测试模式：处理前{sample_size}家公司")
    
    # 多线程处理
    extracted_data = []
    csv_filename = f"csv_data/上市公司年报数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    print(f"开始处理{len(companies_to_process)}家公司的年报数据...")
    
    with ThreadPoolExecutor(max_workers=THREAD_WORKERS) as executor:
        futures = {executor.submit(process_company, company): company for company in companies_to_process}
        
        for future in tqdm(as_completed(futures), total=len(companies_to_process)):
            result = future.result()
            if result:
                extracted_data.append(result)
                
                # 每处理20家公司保存一次
                if len(extracted_data) % 20 == 0:
                    save_to_csv(extracted_data, csv_filename)
                    extracted_data = []
                    print(f"已保存数据，当前总计: {len(extracted_data)}条")
                    time.sleep(30)  # 增加保存后的暂停时间
    
    # 保存剩余数据
    if extracted_data:
        save_to_csv(extracted_data, csv_filename)
    
    print(f"数据处理完成，共提取{len(extracted_data)}家公司的有效数据")

def extract_text_from_pdf(pdf_path):
    """从PDF文件中提取文本"""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
            return text
    except Exception as e:
        print(f"提取PDF文本时出错: {e}")
        return ""

def extract_fields(text, report_info):
    """从年报文本中提取关键字段"""
    data = {
        'company_code': report_info['code'],
        'company_name': report_info['name'],
        'report_year': report_info['year'],
        'report_date': report_info['date']
    }

    # 提取营业收入
    revenue_pattern = r'营业收入[：:\s]*([0-9,，.]+)[万元亿]?'
    revenue_match = re.search(revenue_pattern, text)
    if revenue_match:
        data['revenue'] = revenue_match.group(1).replace(',', '').replace('，', '')
    else:
        data['revenue'] = ''

    # 提取净利润
    profit_pattern = r'净利润[：:\s]*([0-9,，.-]+)[万元亿]?'
    profit_match = re.search(profit_pattern, text)
    if profit_match:
        data['net_profit'] = profit_match.group(1).replace(',', '').replace('，', '')
    else:
        data['net_profit'] = ''

    # 提取总资产
    assets_pattern = r'总资产[：:\s]*([0-9,，.]+)[万元亿]?'
    assets_match = re.search(assets_pattern, text)
    if assets_match:
        data['total_assets'] = assets_match.group(1).replace(',', '').replace('，', '')
    else:
        data['total_assets'] = ''

    return data

def save_to_csv(data_list, filename):
    """保存数据到CSV文件"""
    if not data_list:
        return

    fieldnames = ['company_code', 'company_name', 'report_year', 'report_date',
                  'revenue', 'net_profit', 'total_assets']

    file_exists = os.path.exists(filename)

    with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()

        for data in data_list:
            writer.writerow(data)

def load_backup_companies():
    """备用公司列表（当API失败时使用）"""
    backup_companies = [
        {'code': '000001', 'name': '平安银行', 'market': 'szse'},
        {'code': '000002', 'name': '万科A', 'market': 'szse'},
        {'code': '000858', 'name': '五粮液', 'market': 'szse'},
        {'code': '000876', 'name': '新希望', 'market': 'szse'},
        {'code': '002415', 'name': '海康威视', 'market': 'szse'},
        {'code': '600000', 'name': '浦发银行', 'market': 'sse'},
        {'code': '600036', 'name': '招商银行', 'market': 'sse'},
        {'code': '600519', 'name': '贵州茅台', 'market': 'sse'},
        {'code': '600887', 'name': '伊利股份', 'market': 'sse'},
        {'code': '300059', 'name': '东方财富', 'market': 'szse'},
        {'code': '002594', 'name': '比亚迪', 'market': 'szse'},
        {'code': '000725', 'name': '京东方A', 'market': 'szse'},
        {'code': '600276', 'name': '恒瑞医药', 'market': 'sse'},
        {'code': '000063', 'name': '中兴通讯', 'market': 'szse'},
        {'code': '600030', 'name': '中信证券', 'market': 'sse'}
    ]
    print("使用备用公司列表")
    return backup_companies

def debug_response(response, context=""):
    """调试响应内容"""
    print(f"\n=== 调试信息 ({context}) ===")
    print(f"状态码: {response.status_code}")
    print(f"URL: {response.url}")
    print(f"响应头: {dict(response.headers)}")
    print(f"内容类型: {response.headers.get('content-type', 'unknown')}")
    print(f"响应长度: {len(response.text)}")
    print(f"响应内容前1000字符:")
    print(response.text[:1000])
    print("=" * 50)

if __name__ == '__main__':
    main()